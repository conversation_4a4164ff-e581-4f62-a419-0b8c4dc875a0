'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Edit,
  Save,
  X,
  Link as LinkIcon,
  Mail,
  Calendar,
  Activity,
  Award,
  TrendingUp
} from 'lucide-react';
import { CompleteProfileData, ProfileUpdatePayload } from '@/types/profile';
import { updateUserProfile } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';

interface ProfileOverviewProps {
  profileData: CompleteProfileData;
  onUpdate: (data: CompleteProfileData) => void;
}

export default function ProfileOverview({ profileData, onUpdate }: ProfileOverviewProps) {
  const { session } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    display_name: profileData.user.display_name || '',
    username: profileData.user.username || '',
    bio: profileData.user.bio || '',
    technical_level: profileData.user.technical_level || '',
    social_links: profileData.user.social_links || {},
  });

  const handleEdit = () => {
    setIsEditing(true);
    setError(null);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setError(null);
    // Reset form data
    setFormData({
      display_name: profileData.user.display_name || '',
      username: profileData.user.username || '',
      bio: profileData.user.bio || '',
      technical_level: profileData.user.technical_level || '',
      social_links: profileData.user.social_links || {},
    });
  };

  const handleSave = async () => {
    if (!session?.access_token) return;

    // Basic validation
    if (!formData.display_name.trim()) {
      setError('Display name is required');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const updatePayload: ProfileUpdatePayload = {
        display_name: formData.display_name.trim(),
        username: formData.username.trim() || undefined,
        bio: formData.bio.trim() || undefined,
        technical_level: formData.technical_level &&
          ['beginner', 'intermediate', 'advanced', 'variable'].includes(formData.technical_level)
          ? (formData.technical_level as 'beginner' | 'intermediate' | 'advanced' | 'variable')
          : undefined,
        social_links: formData.social_links,
      };

      console.log('Updating profile with payload:', updatePayload);
      const updatedUser = await updateUserProfile(updatePayload, session.access_token);
      console.log('Profile update response:', updatedUser);
      
      // Update the profile data
      onUpdate({
        ...profileData,
        user: updatedUser,
      });

      setIsEditing(false);
    } catch (err) {
      console.error('Failed to update profile:', err);
      setError(err instanceof Error ? err.message : 'Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialLinkChange = (platform: string, url: string) => {
    setFormData(prev => ({
      ...prev,
      social_links: {
        ...prev.social_links,
        [platform]: url,
      },
    }));
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main Profile Information */}
      <div className="lg:col-span-2 space-y-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>
                Manage your profile information and public details
              </CardDescription>
            </div>
            {!isEditing ? (
              <Button onClick={handleEdit} variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            ) : (
              <div className="flex space-x-2">
                <Button 
                  onClick={handleSave} 
                  size="sm" 
                  disabled={isLoading}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isLoading ? 'Saving...' : 'Save'}
                </Button>
                <Button 
                  onClick={handleCancel} 
                  variant="outline" 
                  size="sm"
                  disabled={isLoading}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              </div>
            )}
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="display_name">Display Name</Label>
                {isEditing ? (
                  <Input
                    id="display_name"
                    value={formData.display_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, display_name: e.target.value }))}
                    placeholder="Your display name"
                  />
                ) : (
                  <p className="mt-1 text-sm text-gray-900">
                    {profileData.user.display_name || 'Not set'}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="username">Username</Label>
                {isEditing ? (
                  <Input
                    id="username"
                    value={formData.username}
                    onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                    placeholder="Your username"
                  />
                ) : (
                  <p className="mt-1 text-sm text-gray-900">
                    {profileData.user.username ? `@${profileData.user.username}` : 'Not set'}
                  </p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="technical_level">Technical Level</Label>
              {isEditing ? (
                <Select 
                  value={formData.technical_level} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, technical_level: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select your technical level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                    <SelectItem value="variable">Variable</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <p className="mt-1 text-sm text-gray-900">
                  {profileData.user.technical_level
                    ? profileData.user.technical_level.charAt(0).toUpperCase() + profileData.user.technical_level.slice(1).toLowerCase()
                    : 'Not set'}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="bio">Bio</Label>
              {isEditing ? (
                <Textarea
                  id="bio"
                  value={formData.bio}
                  onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
                  placeholder="Tell us about yourself..."
                  rows={3}
                />
              ) : (
                <p className="mt-1 text-sm text-gray-900">
                  {profileData.user.bio || 'No bio provided'}
                </p>
              )}
            </div>

            {/* Social Links */}
            <div>
              <Label>Social Links</Label>
              <div className="mt-2 space-y-2">
                {['website', 'twitter', 'linkedin', 'github'].map((platform) => (
                  <div key={platform} className="flex items-center space-x-2">
                    <Label className="w-20 text-xs capitalize">{platform}</Label>
                    {isEditing ? (
                      <Input
                        value={formData.social_links[platform] || ''}
                        onChange={(e) => handleSocialLinkChange(platform, e.target.value)}
                        placeholder={`Your ${platform} URL`}
                        className="flex-1"
                      />
                    ) : (
                      <div className="flex-1">
                        {formData.social_links[platform] ? (
                          <a 
                            href={formData.social_links[platform]} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-sm text-indigo-600 hover:text-indigo-800 flex items-center"
                          >
                            <LinkIcon className="h-3 w-3 mr-1" />
                            {formData.social_links[platform]}
                          </a>
                        ) : (
                          <span className="text-sm text-gray-500">Not set</span>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            {profileData.recent_activity.length > 0 ? (
              <div className="space-y-3">
                {profileData.recent_activity.slice(0, 5).map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <p className="text-sm text-gray-900">{activity.description}</p>
                      <p className="text-xs text-gray-500">
                        {new Date(activity.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {activity.type}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No recent activity</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Sidebar */}
      <div className="space-y-6">
        {/* Account Stats */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="h-5 w-5 mr-2" />
              Account Stats
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Reputation Score</span>
              <Badge variant="secondary">{profileData.stats.reputation_score}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Tools Approved</span>
              <span className="text-sm font-medium">{profileData.stats.tools_approved}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Requests Fulfilled</span>
              <span className="text-sm font-medium">{profileData.stats.requests_fulfilled}</span>
            </div>
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center space-x-2 text-sm">
              <Mail className="h-4 w-4 text-gray-400" />
              <span>{profileData.user.email}</span>
            </div>
            <div className="flex items-center space-x-2 text-sm">
              <Calendar className="h-4 w-4 text-gray-400" />
              <span>Joined {new Date(profileData.stats.member_since).toLocaleDateString()}</span>
            </div>
            <div className="flex items-center space-x-2 text-sm">
              <TrendingUp className="h-4 w-4 text-gray-400" />
              <span>Role: {profileData.user.role}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
